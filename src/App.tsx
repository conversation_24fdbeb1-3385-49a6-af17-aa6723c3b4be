
import './App.css'
import { Signin } from './pages/Signin'
import { BrowserRouter, Routes, Route } from 'react-router-dom'
import  Dashboard  from './pages/Dashboard'
import DashboardLayout from './components/dashboardLayout'
import { JobMela } from './pages/JobMela/JobMela'
import { UserManageMent } from './pages/UserManageMent/UserManageMent'
import { Toaster } from 'sonner'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import JobMelaDetails from './pages/JobMela/JobMelaDetails'
import JobmelaForm from './components/jobmela/addjobmela'

import ProtectedRoute from './components/protectedRoute'
import PublicRoute from './components/publicRoute'
import { CompanyDetails } from './pages/JobMela/CompanyDetails'



function App() {
  const queryClient = new QueryClient()
  return (
  
    <QueryClientProvider client={queryClient}>
      <Toaster />
      <BrowserRouter>
        <Routes>
          <Route element={<PublicRoute />}>
            <Route path="/" element={<Signin />} />
          </Route>
          
          <Route  element={<ProtectedRoute />} >
           <Route path="/dashboard" element={<DashboardLayout />} >
            <Route index element={< Dashboard />} />
            <Route path="jobmela" element={< JobMela />} />
            <Route path="usermanagement" element={< UserManageMent />} />
            <Route path="jobmela/:id" element={< JobMelaDetails />} />
            <Route path="jobmela/add" element={< JobmelaForm />} />
            <Route path='/dashboard/jobMela/:melaId/company/:companyId' element={<CompanyDetails/>} />
        
          </Route>
          </Route>
        </Routes>
      </BrowserRouter>

    </QueryClientProvider>
  )
}

export default App
