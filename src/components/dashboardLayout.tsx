
import Sidebar from './sidebar';
import Header from './dashboard';
import { Link, Outlet } from 'react-router-dom';
import { Breadcrumb, BreadcrumbEllipsis, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from './ui/breadcrumb';

const DashboardLayout = () => {
    return (
        <div className="bg-gray-200">
            <div className="flex h-screen overflow-hidden">

                <Sidebar />
                <div className="flex-1 flex flex-col overflow-hidden">
                    <Header />
                    <Breadcrumb className='px-6 py-4 max-w-7xl mx-auto w-full'>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link to="/">Home</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbEllipsis />
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbLink asChild>
                                    <Link to="/docs/components">Components</Link>
                                </BreadcrumbLink>
                            </BreadcrumbItem>
                            <BreadcrumbSeparator />
                            <BreadcrumbItem>
                                <BreadcrumbPage>Breadcrumb</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>

                    <main className="overflow-auto w-full flex-grow space-y-20">
                         <div className='max-w-7xl mx-auto py-4 px-6'>
                            
                              <Outlet />
                         </div>
                      
                    </main>
                </div>
            </div>
        </div >
    );
};

export default DashboardLayout;
