
import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Button } from "@/components/ui/button"
import { CardContent } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Separator } from "@/components/ui/separator"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { CalendarDays, Clock, FileText, Send, ChevronDown, Check, CalendarIcon, Settings2 } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { postData } from "@/utils/api/postApi"
import { toast } from "sonner"
import { useNavigate } from "react-router-dom"
import { formSchema } from "@/lib/addJobFormSchema"
const districtOptions = [
  { value: "north", label: "North District" },
  { value: "south", label: "South District" },
  { value: "east", label: "East District" },
  { value: "west", label: "West District" },
  { value: "central", label: "Central District" },
  { value: "downtown", label: "Downtown District" },
  { value: "suburban", label: "Suburban District" },
  { value: "industrial", label: "Industrial District" },
]

export default function VenueForm() {
  const navigate = useNavigate()
  const [districtDropdownOpen, setDistrictDropdownOpen] = useState(false)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      vsVenueName: "",
      vsAddress: "",
      dtStartDate: undefined,
      dtEndDate: undefined,
      vsDistrict: "",
      iMaxCapacity: "",
      dtSlotStartTime: "",
      dtSlotEndTime: "",
      vsDescription: "",
    },
  })

  function onSubmit(values: z.infer<typeof formSchema>) {
    try {
      // Format dates for API
      const formattedValues = {
        ...values,
        dtStartDate: values.dtStartDate ? format(values.dtStartDate, "yyyy-MM-dd") : "",
        dtEndDate: values.dtEndDate ? format(values.dtEndDate, "yyyy-MM-dd") : "",
      }

      postData({
        url: '/jobMela/save',
        data: formattedValues
      })
      toast.success('Mela created successfully')
      navigate('/dashboard/jobmela')
    } catch (error) {
      toast.error('Failed to create mela:', error as any)
    }
  }

  return (
    <div className="">
      <div className="">

        <>
          <CardContent className="p-0 bg-white rounded-xl">
            {/* Title Section */}
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="">
                {/* Header Section */}
                <div className="px-6 pt-6">
                  <h1 className="text-3xl font-bold text-slate-900">New Job Mela</h1>
                  <p className="text-slate-600 text-lg">Create a new job mela event</p>

                </div>
                {/* Venue Details Section */}
                <Separator className="my-2" />
                <div className="space-y-6 px-6 py-6">
                  <div className="flex items-center gap-2 mb-4">
                    <Settings2 className="w-5 h-5 text-emerald-600" />
                    <h3 className="text-xl font-semibold text-slate-900">General Configuration</h3>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
                    <div className="lg:col-span-3">
                      <FormField
                        control={form.control}
                        name="vsVenueName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">Mela Name *</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter venue name"
                                className="h-12 border-slate-300 focus:border-emerald-500 focus:ring-emerald-500"
                                {...field}
                              />
                            </FormControl>
                            <FormDescription className="text-slate-500 font-normal italic">Official name of the event</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <div className="lg:col-span-2">
                      <FormField
                        control={form.control}
                        name="vsDistrict"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-slate-700 font-medium">District *</FormLabel>
                            <FormControl>
                              <Popover open={districtDropdownOpen} onOpenChange={setDistrictDropdownOpen}>
                                <PopoverTrigger asChild>
                                  <Button
                                    variant="outline"
                                    role="combobox"
                                    aria-expanded={districtDropdownOpen}
                                    className="w-full h-12 justify-between border-slate-300 focus:border-border-slate-300 focus:ring-emerald-500"
                                  >
                                    {field.value ? (
                                      districtOptions.find(district => district.value === field.value)?.label
                                    ) : (
                                      <span className="text-muted-foreground">Select district...</span>
                                    )}
                                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                                  </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-full p-0" align="start">
                                  <div className="p-2 space-y-1 max-h-60 overflow-y-auto">
                                    {districtOptions.map((district) => (
                                      <div
                                        key={district.value}
                                        className="flex items-center space-x-2 p-2 hover:bg-accent rounded-sm cursor-pointer"
                                        onClick={() => {
                                          field.onChange(district.value)
                                          setDistrictDropdownOpen(false)
                                        }}
                                      >
                                        <span className="text-sm font-normal flex-1">
                                          {district.label}
                                        </span>
                                        {field.value === district.value && (
                                          <Check className="h-4 w-4 text-primary" />
                                        )}
                                      </div>
                                    ))}
                                  </div>
                                </PopoverContent>
                              </Popover>
                            </FormControl>
                            <FormDescription className="text-slate-500 font-normal italic">District location</FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name="iMaxCapacity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">Maximum Capacity *</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              placeholder="Enter maximum capacity"
                              className="h-12 border-slate-300 focus:border-emerald-500 focus:ring-emerald-500"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">Maximum number of attendees</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>


                  {/* Capacity & Description Section */}


                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

                    <FormField
                      control={form.control}
                      name="vsAddress"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">Complete Address *</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Enter complete venue address"
                              className="min-h-[120px] border-slate-300 focus:border-emerald-500 focus:ring-emerald-500 resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">
                            Full address including city and state
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="vsDescription"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium flex items-center gap-2">
                            <FileText className="w-4 h-4" />
                            Event Description *
                          </FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Provide a detailed description of your event..."
                              className="min-h-[120px] border-slate-300 focus:border-emerald-500 focus:ring-emerald-500 resize-none"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">
                            Detailed description of the event (minimum 10 characters)
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>


                <Separator className="my-2" />

                {/* Event Schedule Section */}
                <div className="space-y-6  px-6 py-6 max-w-2xl">
                  <div className="flex items-center gap-2 mb-4">
                    <CalendarDays className="w-5 h-5 text-emerald-600" />
                    <h3 className="text-xl font-semibold text-slate-900">Schedule Configuration</h3>
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="dtStartDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">Start Date *</FormLabel>
                          <FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full h-12 justify-start text-left font-normal border-slate-300 focus:border-slate-300 focus:ring-emerald-500",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {field.value ? format(field.value, "PPP") : "Pick a date"}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) =>
                                    date < new Date(new Date().setHours(0, 0, 0, 0))
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">Event start date</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dtEndDate"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium">End Date *</FormLabel>
                          <FormControl>
                            <Popover>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="outline"
                                  className={cn(
                                    "w-full h-12 justify-start text-left font-normal border-slate-300 focus:border-slate-300 focus:ring-emerald-500",
                                    !field.value && "text-muted-foreground"
                                  )}
                                >
                                  <CalendarIcon className="mr-2 h-4 w-4" />
                                  {field.value ? format(field.value, "PPP") : "Pick a date"}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="start">
                                <Calendar
                                  mode="single"
                                  selected={field.value}
                                  onSelect={field.onChange}
                                  disabled={(date) => {
                                    const startDate = form.getValues("dtStartDate")
                                    return date < new Date(new Date().setHours(0, 0, 0, 0)) ||
                                      (startDate && date < startDate)
                                  }}
                                />
                              </PopoverContent>
                            </Popover>
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">Event end date</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <FormField
                      control={form.control}
                      name="dtSlotStartTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            Start Time *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="time"
                              className="h-12 border-slate-300 focus:border-emerald-500 focus:ring-emerald-500"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">Daily start time</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="dtSlotEndTime"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-slate-700 font-medium flex items-center gap-2">
                            <Clock className="w-4 h-4" />
                            End Time *
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="time"
                              className="h-12 border-slate-300 focus:border-emerald-500 focus:ring-emerald-500"
                              {...field}
                            />
                          </FormControl>
                          <FormDescription className="text-slate-500 font-normal italic">Daily end time</FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>



                <Separator className="my-2" />

                {/* Submit Button */}
                <div className="flex justify-center pt-4 px-6 py-6">
                  <Button
                    type="submit"
                    size="lg"
                    className="w-full lg:w-auto px-12 py-4 bg-gradient-to-r from-emerald-600 to-emerald-700 hover:from-emerald-700 hover:to-emerald-800 text-white font-semibold rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"
                  >
                    Submit Registration
                    <Send className="w-4 h-4 ml-2" />
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </>
      </div>
    </div>
  )
}
