import { Collapsible } from "@radix-ui/react-collapsible";
import { <PERSON>, CardHeader, CardTitle } from "../ui/card";
import {  Briefcase, Building2, Calendar, ChevronDown, ChevronUp, Clock, Edit, Eye, MapPin, Users } from "lucide-react";
import { useState } from "react";
import { Badge } from "../ui/badge";
import { Switch } from "../ui/switch";
import { CollapsibleContent, CollapsibleTrigger } from "../ui/collapsible";
import { Button } from "../ui/button";
import { formatDate } from "@/lib/formateDate";
import { formatTime } from "@/lib/formateTime";
import { CompanyCard } from "./companyCard";
import { useMelaToggle } from "@/hooks/useToggleMelaStatus";

export function MelaContainer({ melaInfo, companies }: { melaInfo: any; companies: any[] }) {
  const { check, handleToggle, isUpdating } = useMelaToggle(melaInfo)
  const isActive = melaInfo.bActive === 1
  const startDate = new Date(melaInfo.start_date)
  const endDate = new Date(melaInfo.end_date)
  const currentDate = new Date()

  const getEventStatus = () => {
   
    if (currentDate < startDate) return { status: "Upcoming", color: "bg-blue-100 text-blue-800" }
    if (currentDate > endDate) return { status: "Completed", color: "bg-gray-100 text-gray-800" }
     if (!isActive) return { status: "Inactive", color: "bg-red-100 text-red-800" }
    return { status: "Active", color: "bg-green-100 text-green-800" }
  }
  const eventStatus = getEventStatus()

  const [isExpanded, setIsExpanded] = useState(true)
  return (
    <div className="mb-8">
      {/* Mela Header Card */}
      <Card className="border-2 bg-gray-100 shadow-lg">
        <CardHeader className="pb-4">
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-4 mb-3">
                  <div className="p-3 bg-blue-600 rounded-xl shadow-md">
                    <Building2 className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <CardTitle className="text-3xl font-bold text-gray-900 mb-1">
                      {melaInfo.venueName} Job Mela
                    </CardTitle>
                    <p className="text-gray-600">
                      Event ID: {melaInfo.melaId} • {melaInfo.district}
                    </p>
                  </div>
                </div>

                <div className="flex flex-wrap gap-3 mb-4">
                  <Badge className={`${eventStatus.color} border-0 px-3 py-1`}>{eventStatus.status}</Badge>
                  <Badge variant="outline" className="px-3 py-1">
                    <Users className="mr-1 h-3 w-3" />
                    {companies.length} Companies
                  </Badge>
                  <Badge variant="outline" className="px-3 py-1">
                    <Briefcase className="mr-1 h-3 w-3" />
                    {/* {totalJobs} Job Openings */}
                  </Badge>
                </div>
              </div>

              <div className="flex items-center gap-2">

                <Switch
                  id="mela-status"
                  onClick={handleToggle}
                  disabled={eventStatus.status === "Completed" || isUpdating}
                  checked={check}
                />
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                    <span className="sr-only">Toggle details</span>
                  </Button>
                </CollapsibleTrigger>
              </div>
            </div>

            <CollapsibleContent className="space-y-4">
              {/* Event Description */}
              <div className="bg-white p-4 rounded-lg border border-blue-100">

                <p className="text-gray-700 leading-relaxed">{melaInfo.description}</p>
              </div>

              {/* Event Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Date Range */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Duration</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.start_date)}</p>
                    <p className="text-xs text-gray-500">to</p>
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.end_date)}</p>
                  </div>
                </div>

                {/* Location */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-green-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Location</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{melaInfo.address}</p>
                    <p className="text-xs text-gray-600">{melaInfo.district}</p>
                  </div>
                </div>

                {/* Time Slots */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="h-4 w-4 text-orange-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Daily Hours</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatTime(melaInfo.dtSlotStartTime)}</p>
                    <p className="text-xs text-gray-500">to</p>
                    <p className="text-sm font-medium text-gray-900">{formatTime(melaInfo.dtSlotEndTime)}</p>
                  </div>
                </div>

                {/* Management */}
                <div className="bg-white p-4 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2 mb-2">
                    <Edit className="h-4 w-4 text-purple-600" />
                    <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Last Updated</span>
                  </div>
                  <div className="space-y-1">
                    <p className="text-sm font-medium text-gray-900">{formatDate(melaInfo.dtUpdatedAt)}</p>
                    <p className="text-xs text-gray-600">Created: {formatDate(melaInfo.dtCreatedAt)}</p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-2">
                <Button className="bg-blue-600 hover:bg-blue-700">
                  <Building2 className="mr-2 h-4 w-4" />
                  Manage Mela
                </Button>
                <Button variant="outline">
                  <Eye className="mr-2 h-4 w-4" />
                  View Reports
                </Button>
                <Button variant="outline">
                  <Calendar className="mr-2 h-4 w-4" />
                  Schedule
                </Button>

              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardHeader>
      </Card>

      {/* Companies Section */}
      <div className="mt-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-600" />
            Participating Companies ({companies.length})
          </h3>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 pl-4 border-l-2 border-blue-100">
          {companies.map((company: any) => (
            <CompanyCard key={company.companyName} company={company} />
          ))}
        </div>
      </div>
    </div>
  )
}