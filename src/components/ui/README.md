# Custom Pickers Documentation

This directory contains custom date and time picker components built with React and TypeScript.

## Components

### 1. CustomDatePicker

A fully customizable date picker component with dropdown interface.

#### Features
- **Controlled and Uncontrolled modes**: Can be used with or without external state management
- **Quick selects**: Predefined date options for quick selection
- **Date validation**: Min/max date constraints
- **Intuitive UI**: Dropdown with day/month/year selectors
- **Responsive design**: Works on mobile and desktop

#### Props

```typescript
interface CustomDatePickerProps {
  value?: DateValue;              // Current selected date
  onChange?: (val: DateValue) => void; // Callback when date changes
  label?: string;                 // Display label
  quickSelects?: DateValue[];     // Array of quick select dates
  minDate?: DateValue;           // Minimum selectable date
  maxDate?: DateValue;           // Maximum selectable date
}

type DateValue = {
  day: number;    // 1-31
  month: number;  // 1-12
  year: number;   // Full year (e.g., 2024)
};
```

#### Usage Examples

**Basic Usage (Uncontrolled)**
```tsx
import CustomDatePicker from '@/components/ui/customdatepicker';

<CustomDatePicker
  label="Select Date"
  onChange={(date) => console.log(date)}
/>
```

**Controlled with State**
```tsx
const [selectedDate, setSelectedDate] = useState<DateValue>({
  day: 15,
  month: 6,
  year: 2024
});

<CustomDatePicker
  label="Event Date"
  value={selectedDate}
  onChange={setSelectedDate}
/>
```

**With Constraints and Quick Selects**
```tsx
<CustomDatePicker
  label="Deadline"
  minDate={{ day: 1, month: 1, year: 2024 }}
  maxDate={{ day: 31, month: 12, year: 2025 }}
  quickSelects={[
    { day: 1, month: 1, year: 2024 },    // New Year
    { day: 25, month: 12, year: 2024 },  // Christmas
  ]}
  onChange={setDate}
/>
```

### 2. CustomTimePicker

A time picker component with AM/PM support.

#### Features
- **12-hour format**: Hours 1-12 with AM/PM
- **Minute precision**: 0-59 minutes
- **Quick selects**: Predefined time options
- **Intuitive controls**: Up/down arrows for adjustment

#### Props

```typescript
interface CustomTimePickerProps {
  value?: TimeValue;
  onChange?: (val: TimeValue) => void;
  label?: string;
  quickSelects?: TimeValue[];
}

type TimeValue = {
  hours: number;        // 1-12
  minutes: number;      // 0-59
  period: 'AM' | 'PM';
};
```

#### Usage Examples

**Basic Usage**
```tsx
import CustomTimePicker from '@/components/ui/customtimepicker';

<CustomTimePicker
  label="Meeting Time"
  onChange={(time) => console.log(time)}
/>
```

**With Quick Selects**
```tsx
<CustomTimePicker
  label="Appointment Time"
  quickSelects={[
    { hours: 9, minutes: 0, period: 'AM' },   // 9:00 AM
    { hours: 12, minutes: 0, period: 'PM' },  // 12:00 PM
    { hours: 5, minutes: 30, period: 'PM' },  // 5:30 PM
  ]}
  onChange={setTime}
/>
```

### 3. CustomDateTimePicker

A combined date and time picker component.

#### Features
- **Unified interface**: Date and time selection in one component
- **Separate controls**: Individual date and time pickers
- **Multiple formats**: ISO string, timestamp outputs
- **All features**: Inherits all features from individual pickers

#### Props

```typescript
interface CustomDateTimePickerProps {
  value?: DateTimeValue;
  onChange?: (val: DateTimeValue) => void;
  label?: string;
  dateQuickSelects?: DateValue[];
  timeQuickSelects?: TimeValue[];
  minDate?: DateValue;
  maxDate?: DateValue;
}

type DateTimeValue = {
  date: DateValue;
  time: TimeValue;
};
```

#### Usage Examples

**Basic Usage**
```tsx
import CustomDateTimePicker from '@/components/ui/customdatetimepicker';

<CustomDateTimePicker
  label="Event Schedule"
  onChange={(dateTime) => console.log(dateTime)}
/>
```

**Full Configuration**
```tsx
<CustomDateTimePicker
  label="Meeting Schedule"
  dateQuickSelects={[
    { day: 1, month: 1, year: 2024 },
    { day: 25, month: 12, year: 2024 },
  ]}
  timeQuickSelects={[
    { hours: 9, minutes: 0, period: 'AM' },
    { hours: 2, minutes: 0, period: 'PM' },
  ]}
  minDate={{ day: 1, month: 1, year: 2024 }}
  maxDate={{ day: 31, month: 12, year: 2025 }}
  onChange={setDateTime}
/>
```

## Styling

All components use Tailwind CSS classes and follow the existing design system:

- **Primary colors**: Blue tones for active states
- **Gray scale**: For borders, backgrounds, and text
- **Hover effects**: Subtle transitions and color changes
- **Focus states**: Ring effects for accessibility

## Accessibility

- **Keyboard navigation**: All interactive elements are keyboard accessible
- **ARIA labels**: Proper labeling for screen readers
- **Focus management**: Clear focus indicators
- **Semantic HTML**: Proper button and form elements

## Browser Support

- **Modern browsers**: Chrome, Firefox, Safari, Edge
- **Mobile responsive**: Touch-friendly interface
- **TypeScript**: Full type safety

## Demo

Visit `/dashboard/pickers-demo` to see all components in action with various configurations and examples.

## Integration with Forms

These components work well with form libraries like React Hook Form:

```tsx
import { useForm, Controller } from 'react-hook-form';

const MyForm = () => {
  const { control, handleSubmit } = useForm();

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Controller
        name="eventDate"
        control={control}
        render={({ field }) => (
          <CustomDatePicker
            label="Event Date"
            value={field.value}
            onChange={field.onChange}
          />
        )}
      />
    </form>
  );
};
```
