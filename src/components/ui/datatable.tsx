import {
    useReactTable,
    getCoreRowModel,
    getPaginationRowModel,
    flexRender,
    type ColumnDef,
  } from '@tanstack/react-table';
  import { Button } from '@/components/ui/button';
  import { ChevronLeft, ChevronRight } from 'lucide-react';
import { NotFound } from '../notfound';
  interface DataTableProps<TData> {
    columns: ColumnDef<TData, any>[];
    data: TData[];
    pageCount: number;
    pageIndex: number;
    pageSize: number;
    onPageChange: (pageIndex: number) => void;
  }
  export const DataTable = <TData,>({
    columns,
    data,
    pageCount,
    pageIndex,
    pageSize,
    onPageChange,
  }: DataTableProps<TData>) => {
    const table = useReactTable({
      data,
      columns,
      pageCount,
      state: {
        pagination: {
          pageIndex,
          pageSize,
        },
      },
      manualPagination: true,
      onPaginationChange: (updater) => {
        const newState =
          typeof updater === 'function' ? updater({ pageIndex, pageSize }) : updater;
        onPageChange(newState.pageIndex);
      },
      getCoreRowModel: getCoreRowModel(),
      getPaginationRowModel: getPaginationRowModel(),
    });
  
    return (
      <div className="w-full  bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
          


        {/* Table Container */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className=" border-b border-gray-200 rounded-xl">
              {table.getHeaderGroups().map(headerGroup => (
                <tr key={headerGroup.id}>
                  {headerGroup.headers.map(header => (
                    <th 
                      key={header.id}
                      className="px-6 py-4 text-left text-xs font-semibold text-gray-700 uppercase tracking-wider"
                    >
                      {flexRender(header.column.columnDef.header, header.getContext())}
                    </th>
                  ))}
                </tr>
              ))}
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {table.getRowModel().rows.map((row, index) => (
                <tr 
                  key={row.id}
                  className={`hover:bg-gray-50 transition-colors duration-150 ${
                    index % 2 === 0 ? 'bg-white' : 'bg-gray-25'
                  }`}
                >
                  {row.getVisibleCells().map(cell => (
                    <td 
                      key={cell.id}
                      className="px-6 py-4 text-sm text-gray-900 whitespace-nowrap"
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
  
        {/* Empty State */}
        {data.length === 0 && (
   
            <NotFound/>
          
        )}
  
        {/* Pagination Controls - Centered */}
        <div className="px-6 py-4 border-t border-gray-200 flex items-center gap-4 justify-between">
          {/* Page Information */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-700 font-medium">
                Showing  {table.getState().pagination.pageIndex + 1} of {pageCount}
              </span>
              <span className="text-xs text-gray-500">
                ({data.length} {data.length === 1 ? 'item' : 'items'})
              </span>
            </div>
          <div >
                      {pageCount > 1 && (
            <div className="flex items-center justify-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Previous</span>
            </Button>
  
            
  
              <div className="flex justify-center ">
                <div className="flex space-x-1">
                  {/* First Page */}
                  {pageIndex > 2 && (
                    <>
                      <button
                        onClick={() => onPageChange(0)}
                        className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                      >
                        1
                      </button>
                      {pageIndex > 3 && (
                        <span className="px-2 py-1 text-sm text-gray-400">...</span>
                      )}
                    </>
                  )}
    
                  {/* Previous Pages */}
                  {pageIndex > 0 && (
                    <button
                      onClick={() => onPageChange(pageIndex - 1)}
                      className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      {pageIndex}
                    </button>
                  )}
    
                  {/* Current Page */}
                  <button
                    className="px-3 py-1 text-sm text-white bg-blue-600 rounded-md font-medium"
                    disabled
                  >
                    {pageIndex + 1}
                  </button>
    
                  {/* Next Pages */}
                  {pageIndex < pageCount - 1 && (
                    <button
                      onClick={() => onPageChange(pageIndex + 1)}
                      className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                    >
                      {pageIndex + 2}
                    </button>
                  )}
    
                  {/* Last Page */}
                  {pageIndex < pageCount - 3 && (
                    <>
                      {pageIndex < pageCount - 4 && (
                        <span className="px-2 py-1 text-sm text-gray-400">...</span>
                      )}
                      <button
                        onClick={() => onPageChange(pageCount - 1)}
                        className="px-3 py-1 text-sm text-gray-700 hover:bg-gray-100 rounded-md transition-colors"
                      >
                        {pageCount}
                      </button>
                    </>
                  )}
                </div>
              </div>

            {/* Next Button */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="flex items-center space-x-1 px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span>Next</span>
              <ChevronRight className="h-4 w-4" />
            </Button>
            </div>
                        )} 
          </div>
  

        </div>
      </div>
    );
  };