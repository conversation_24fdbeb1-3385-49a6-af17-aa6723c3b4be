"use client"

import { Input } from "@/components/ui/input"
import { Search } from "lucide-react"
import { useState } from "react"

type SearchBarProps = {
  placeholder?: string
  onSearch: (query: string) => void
  initialValue?: string
  searchOnEnterOnly?: boolean
}

export default function SearchBar({
  placeholder = "Search...",
  onSearch,
  initialValue = "",
  searchOnEnterOnly = true,
}: SearchBarProps) {
  const [query, setQuery] = useState(initialValue)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    setQuery(value)
    if (!searchOnEnterOnly) {
      onSearch(value)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (searchOnEnterOnly && e.key === "Enter") {
      onSearch(query)
    }
  }

  return (
    <div className="relative w-full max-w-sm">
      <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground h-4 w-4" />
      <Input
        type="text"
        value={query}
        onChange={handleChange}
        onKeyDown={handleKeyDown}
        placeholder={placeholder}
        className="pl-10"
      />
    </div>
  )
}
