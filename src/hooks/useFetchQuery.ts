// hooks/usePostQuery.ts
import { useQuery } from '@tanstack/react-query';
import { postData } from '@/utils/api/postApi';


interface UsePostQueryOptions {
  enabled?: boolean;
  queryKey: string[];
  payload: any;
  showToast?: boolean;
  staleTime?: number;
}

export const usePostQuery = ({
  queryKey,
  payload,
  staleTime = 0,
  enabled = true,
}: UsePostQueryOptions) => {
  return useQuery({
    queryKey,
    queryFn: () => postData({ url: queryKey[0], data: payload }),
    staleTime,
    enabled,
  });
};
