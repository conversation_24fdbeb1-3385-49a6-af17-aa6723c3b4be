
import { useParams } from 'react-router-dom'
import { usePostQuery } from './useFetchQuery'

export const useJobData = () => {
  const melaId = useParams().melaId
  const companyId = useParams().companyId
  const payload = { melaId ,companyId  }
  const { data, isLoading, isError } = usePostQuery({
    queryKey: ['/jobMela/joblist', JSON.stringify(payload)],
    payload,
    staleTime: 1000 * 60 * 5,
  })
  return {
    jobInfo: data?.data.jobs,
    
    isLoading,
    isError
  }
}