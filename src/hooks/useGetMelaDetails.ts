import { useParams } from 'react-router-dom'
import { usePostQuery } from './useFetchQuery'

export const useMelaData = () => {
  const melaId = useParams().id
  const payload = { melaId }
  const { data, isLoading, isError } = usePostQuery({
    queryKey: ['/jobMela/CompanyList', JSON.stringify(payload)],
    payload,
    staleTime: 1000 * 60 * 5,
  })
  return {
    melaInfo: data?.data.melaInfo,
    companies: data?.data.companies || [],
    isLoading,
    isError
  }
}