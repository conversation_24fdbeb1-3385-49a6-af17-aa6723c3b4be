import { useState, useEffect } from 'react'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { postData } from '@/utils/api/postApi'
export const useMelaToggle = (melaInfo: any) => {
  const [check, setCheck] = useState(melaInfo?.bActive === 1)
  const queryClient = useQueryClient()
  const updateMelaMutation = useMutation({
    mutationFn: (payload: any) => postData({
      url: '/jobMela/verify',
      data: payload
    }),
    onSuccess: () => {
      toast.success('Mela status updated successfully')
      queryClient.invalidateQueries({ queryKey: ['/jobMela/CompanyList'] })
    },
    onError: (error) => {
      toast.error('Failed to update mela status:', error as any)
    }
  })
  useEffect(() => {
    setCheck(melaInfo?.bActive === 1)
  }, [melaInfo])
  const handleToggle = () => {
    const newChecked = !check
    setCheck(newChecked)
    const payload = {
      melaId: melaInfo.melaId,
      bActive: newChecked ? 1 : 0,
      vsRemarks: "Status updated"
    }
    updateMelaMutation.mutate(payload)
  }

  return {
    check,
    handleToggle,
    isUpdating: updateMelaMutation.isPending
  }
}