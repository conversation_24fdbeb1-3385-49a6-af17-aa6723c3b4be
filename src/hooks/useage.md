# useFetchQuery Hook

The `useFetchQuery` hook is a custom React hook that simplifies fetching data from an API using React Query. It provides a convenient way to handle data fetching, caching, and error handling.

## Usage

To use the `useFetchQuery` hook, you need to import it from the appropriate file. Here's an example of how to use it:

```tsx
import { useFetchQuery } from 'path/to/useFetchQuery';

function MyComponent() {
  const { data, isLoading, isError } = useFetchQuery({
    queryKey: ['/api/data'],
    payload: { id: 1 },
    showToast: true,
    staleTime: 1000 * 60 * 5,
  });

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (isError) {
    return <div>Error fetching data</div>;
  }

  return <div>{data}</div>;
}
```