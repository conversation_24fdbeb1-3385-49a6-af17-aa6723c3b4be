import * as z from "zod"


export const formSchema = z.object({
  vsVenueName: z.string().min(2, {
    message: "Venue name must be at least 2 characters.",
  }),
  vsAddress: z.string().min(5, {
    message: "Address must be at least 5 characters.",
  }),
  dtStartDate: z.date({
    message: "Start date is required.",
  }),
  dtEndDate: z.date({
    message: "End date is required.",
  }),
  vsDistrict: z.string().min(2, {
    message: "District must be at least 2 characters.",
  }),
  iMaxCapacity: z.string().min(1, {
    message: "Capacity must be at least 1.",
  }),
  dtSlotStartTime: z.string().min(1, {
    message: "Start time is required.",
  }),
  dtSlotEndTime: z.string().min(1, {
    message: "End time is required.",
  }),
  vsDescription: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
})