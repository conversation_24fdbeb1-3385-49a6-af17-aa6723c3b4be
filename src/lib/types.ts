export interface UserState {
    user: any | null;
    username :string | null;
    isEnabled :boolean | null;
    lastLogin :string | null;
    token: string | null;
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
    error: string | null;
    role:string | null ;
  }


  export interface JobMelaItem {
    melaId: number;
    venueName: string;
    address: string;
    district: string;
    description: string;
    dtSlotStartTime: string;
    dtSlotEndTime: string;
    bActive: number;
    dtCreatedAt: string;
    dtUpdatedAt: string | null;
    start_date: string;
    end_date: string;
  }
  
  export interface Pagination {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNextPage: boolean;
    hasPrevPage: boolean;
  }
  
  export interface JobMelaState {
    jobMelas: JobMelaItem[];   
    pagination: Pagination | null;
    status: 'idle' | 'loading' | 'succeeded' | 'failed';
    error: string | null;
    selectedJobMela: JobMelaItem | null; 
  }
  export interface DecodedToken {
    userId: number;
    username: string;
    role: string;
    isEnabled?: boolean;
    lastLogin?: string | null;
    exp: number;
    iat: number;
  }
  export interface Company {
  pklCompanyId: number;
  vsCompanyName: string;
  vsAddress: string;
  vsDistrict: string;
  bActive: boolean;
}

export interface CompanyData {
  companies: Company[];
  total: number;
}

export interface CompanyListResponse {
  success: boolean;
  message: string;
  data: CompanyData;
}
