import { 
  Users, 
  Building2, 
  Calendar, 
  ArrowUpRight,
  ArrowDownRight,
  UserCheck,
  MapPin,
  Clock,
  CheckCircle,
  UserPlus,
  Target
} from 'lucide-react';

const Dashboard = () => {
  const stats = [
    {
      title: "Total Users",
      value: "24,847",
      change: "+18.2%",
      trend: "up",
      icon: Users,
      color: "text-blue-600",
      description: "Active job seekers"
    },
    {
      title: "Job Melas",
      value: "156",
      change: "+12.5%",
      trend: "up",
      icon: Calendar,
      color: "text-green-600",
      description: "This month"
    },
    {
      title: "Companies Registered",
      value: "1,892",
      change: "+8.7%",
      trend: "up",
      icon: Building2,
      color: "text-purple-600",
      description: "Total employers"
    },
    {
      title: "Job Placements",
      value: "3,421",
      change: "+22.1%",
      trend: "up",
      icon: UserCheck,
      color: "text-orange-600",
      description: "Successfully placed"
    }
  ];

  const recentActivity = [
    { action: "New company registered: Tech Solutions Ltd", time: "5 minutes ago", type: "company", icon: Building2 },
    { action: "Job Mela scheduled in Mumbai", time: "12 minutes ago", type: "mela", icon: Calendar },
    { action: "1,250 new user registrations today", time: "25 minutes ago", type: "user", icon: UserPlus },
    { action: "Job fair completed in Delhi - 450 attendees", time: "2 hours ago", type: "success", icon: CheckCircle },
    { action: "Bulk company verification completed", time: "4 hours ago", type: "system", icon: Target },
  ];

  const upcomingJobMelas = [
    { 
      title: "Tech Career Fair 2025", 
      location: "Bangalore", 
      date: "Aug 12, 2025", 
      companies: 85, 
      status: "upcoming",
      attendees: "2,100+ registered"
    },
    { 
      title: "Healthcare Jobs Expo", 
      location: "Mumbai", 
      date: "Aug 15, 2025", 
      companies: 62, 
      status: "upcoming",
      attendees: "1,850+ registered"
    },
    { 
      title: "Banking & Finance Mela", 
      location: "Delhi", 
      date: "Aug 18, 2025", 
      companies: 45, 
      status: "upcoming",
      attendees: "1,200+ registered"
    },
    { 
      title: "Manufacturing Jobs Fair", 
      location: "Chennai", 
      date: "Aug 22, 2025", 
      companies: 78, 
      status: "planning",
      attendees: "Registration opens soon"
    },
  ];

  const topCompanies = [
    { name: "Infosys Limited", jobs: 245, applications: 12450, logo: "I" },
    { name: "Tata Consultancy Services", jobs: 189, applications: 9870, logo: "T" },
    { name: "Wipro Technologies", jobs: 156, applications: 8920, logo: "W" },
    { name: "HCL Technologies", jobs: 134, applications: 7650, logo: "H" },
  ];

  const chartData = [
    { month: "Feb", users: 18500, companies: 1420, melas: 98 },
    { month: "Mar", users: 19200, companies: 1580, melas: 115 },
    { month: "Apr", users: 20800, companies: 1650, melas: 128 },
    { month: "May", users: 22100, companies: 1720, melas: 142 },
    { month: "Jun", users: 23400, companies: 1790, melas: 148 },
    { month: "Jul", users: 24200, companies: 1850, melas: 156 },
    { month: "Aug", users: 24847, companies: 1892, melas: 156 },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Main Content */}
      <main className="p-6">
        {/* Welcome Section */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Employment Dashboard Overview</h2>
          <p className="text-gray-600">Track user registrations, job fairs, company partnerships, and placement success.</p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-lg bg-gray-50`}>
                  <stat.icon className={`w-6 h-6 ${stat.color}`} />
                </div>
                <div className={`flex items-center space-x-1 text-sm font-medium ${stat.trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {stat.trend === 'up' ? <ArrowUpRight className="w-4 h-4" /> : <ArrowDownRight className="w-4 h-4" />}
                  <span>{stat.change}</span>
                </div>
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
                <p className="text-sm text-gray-600">{stat.title}</p>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </div>
            </div>
          ))}
        </div>

        {/* Charts and Activity Row */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Line Chart */}
          <div className="lg:col-span-2 bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Platform Growth</h3>
              <div className="flex items-center space-x-4 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-gray-600">Users</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                  <span className="text-gray-600">Companies</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-gray-600">Job Melas</span>
                </div>
              </div>
            </div>
            <div className="h-64 relative">
              <svg viewBox="0 0 700 200" className="w-full h-full">
                {/* Grid lines */}
                {[0, 50, 100, 150, 200].map((y) => (
                  <line key={y} x1="50" y1={y} x2="650" y2={y} stroke="#f3f4f6" strokeWidth="1" />
                ))}
                {chartData.map((_, index) => (
                  <line key={index} x1={50 + (index * 85)} y1="0" x2={50 + (index * 85)} y2="200" stroke="#f3f4f6" strokeWidth="1" />
                ))}
                
                {/* Users line */}
                <polyline
                  fill="none"
                  stroke="#3b82f6"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points={chartData.map((data, index) => 
                    `${50 + (index * 85)},${200 - (data.users / 25000) * 180}`
                  ).join(' ')}
                />
                
                {/* Companies line */}
                <polyline
                  fill="none"
                  stroke="#8b5cf6"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points={chartData.map((data, index) => 
                    `${50 + (index * 85)},${200 - (data.companies / 2000) * 180}`
                  ).join(' ')}
                />
                
                {/* Job Melas line */}
                <polyline
                  fill="none"
                  stroke="#10b981"
                  strokeWidth="3"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points={chartData.map((data, index) => 
                    `${50 + (index * 85)},${200 - (data.melas / 200) * 180}`
                  ).join(' ')}
                />
                
                {/* Data points for Users */}
                {chartData.map((data, index) => (
                  <circle
                    key={`users-${index}`}
                    cx={50 + (index * 85)}
                    cy={200 - (data.users / 25000) * 180}
                    r="4"
                    fill="#3b82f6"
                    className="hover:r-6 transition-all cursor-pointer"
                  >
                    <title>Users: {data.users.toLocaleString()}</title>
                  </circle>
                ))}
                
                {/* Data points for Companies */}
                {chartData.map((data, index) => (
                  <circle
                    key={`companies-${index}`}
                    cx={50 + (index * 85)}
                    cy={200 - (data.companies / 2000) * 180}
                    r="4"
                    fill="#8b5cf6"
                    className="hover:r-6 transition-all cursor-pointer"
                  >
                    <title>Companies: {data.companies.toLocaleString()}</title>
                  </circle>
                ))}
                
                {/* Data points for Job Melas */}
                {chartData.map((data, index) => (
                  <circle
                    key={`melas-${index}`}
                    cx={50 + (index * 85)}
                    cy={200 - (data.melas / 200) * 180}
                    r="4"
                    fill="#10b981"
                    className="hover:r-6 transition-all cursor-pointer"
                  >
                    <title>Job Melas: {data.melas}</title>
                  </circle>
                ))}
                
                {/* Month labels */}
                {chartData.map((data, index) => (
                  <text
                    key={`label-${index}`}
                    x={50 + (index * 85)}
                    y="220"
                    textAnchor="middle"
                    className="fill-gray-500 text-xs"
                  >
                    {data.month}
                  </text>
                ))}
              </svg>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
            <div className="space-y-4">
              {recentActivity.map((activity, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className={`p-1.5 rounded-full mt-0.5 ${
                    activity.type === 'user' ? 'bg-blue-100' :
                    activity.type === 'company' ? 'bg-purple-100' :
                    activity.type === 'mela' ? 'bg-green-100' :
                    activity.type === 'success' ? 'bg-orange-100' : 'bg-gray-100'
                  }`}>
                    <activity.icon className={`w-3 h-3 ${
                      activity.type === 'user' ? 'text-blue-600' :
                      activity.type === 'company' ? 'text-purple-600' :
                      activity.type === 'mela' ? 'text-green-600' :
                      activity.type === 'success' ? 'text-orange-600' : 'text-gray-600'
                    }`} />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900 leading-tight">{activity.action}</p>
                    <p className="text-xs text-gray-500 flex items-center mt-1">
                      <Clock className="w-3 h-3 mr-1" />
                      {activity.time}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Row */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Upcoming Job Melas */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Upcoming Job Melas</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
            </div>
            <div className="space-y-4">
              {upcomingJobMelas.map((mela, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-semibold text-gray-900 mb-1">{mela.title}</h4>
                      <div className="flex items-center text-sm text-gray-600 space-x-4">
                        <div className="flex items-center">
                          <MapPin className="w-4 h-4 mr-1" />
                          <span>{mela.location}</span>
                        </div>
                        <div className="flex items-center">
                          <Calendar className="w-4 h-4 mr-1" />
                          <span>{mela.date}</span>
                        </div>
                      </div>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      mela.status === 'upcoming' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'
                    }`}>
                      {mela.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{mela.companies} companies • {mela.attendees}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Top Companies */}
          <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Top Hiring Companies</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">View All</button>
            </div>
            <div className="space-y-4">
              {topCompanies.map((company, index) => (
                <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{company.logo}</span>
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{company.name}</p>
                      <p className="text-sm text-gray-500">{company.jobs} active jobs</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{company.applications.toLocaleString()}</p>
                    <p className="text-xs text-gray-500">applications</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;