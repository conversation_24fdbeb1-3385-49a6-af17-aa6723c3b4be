
import Multiselectfilter from "@/components/filters/multiselectfilter";
import { DataTable } from "@/components/ui/datatable";
import SearchBar from "@/components/ui/searchbar";
import { useJobData } from "@/hooks/useGetCompanyDetails"
import type { JobMelaItem } from "@/lib/types";
import type { ColumnDef } from "@tanstack/react-table";
import { Loader } from "lucide-react";
export function CompanyDetails(){

    const {jobInfo , isLoading} = useJobData()


      const columns: ColumnDef<JobMelaItem>[] = [
    
        {
          accessorKey: "SL No",
          header: "SL No",
          cell: (info) => info.row.index + 1,
        },
    
        {
          accessorKey: "post_name",
          header: "Job Title",
        },
        {
          accessorKey: "vacancy",
          header: "Vacancy",
        },
    
        {
          accessorKey: "vsSelectionProcedure",
          header: "Interview Procedure",
        },
    
        {
          accessorKey: "vsQualification",
          header: "Min Qualification",
        },
        {
          accessorKey: "company_name",
          header: "Company Name",
        },
    
        {
          accessorKey: "vsVenueName",
          header: "Venue",
         
        }
      ];
    



  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">
      <Loader className="h-8 w-8 animate-spin" />
    </div>;
  }
    return <> 

          <div className=''>
        <h1 className="text-2xl font-bold text-gray-900 mb-2"> List of Job Melas </h1>

        <div className=" bg-white px-6 py-4  flex items-center justify-between border-gray-200 rounded-xl mb-2 ">
          <div >
            <SearchBar onSearch={()=>{}} />
          </div>
          <div className="flex items-center gap-1">
            <Multiselectfilter />
          </div>

        </div>
        <DataTable<JobMelaItem>
          columns={columns}
          data={ jobInfo || []} 


        />
      </div>
        
        
    </>
}