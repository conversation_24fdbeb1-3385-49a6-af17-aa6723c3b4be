import {
  Loader2,
} from "lucide-react"
import { NotFound } from "@/components/notfound"
import { MelaContainer } from "@/components/jobmela/melaContainer"
import { useMelaData } from "@/hooks/useGetMelaDetails"

export default function JobMelaDetails() {
  const { melaInfo, companies, isLoading, isError } = useMelaData()
  if (isLoading) {
    return <div className="flex justify-center items-center h-screen">
      <Loader2 className="h-8 w-8 animate-spin" />
    </div>;
  }
  if (isError || !melaInfo) {
    return <div>
      <NotFound />
    </div>;
  }
  return (
    <div className="p-6">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">Job Mela Admin Dashboard</h1>
          <p className="text-gray-600 text-lg">Manage job mela events and participating companies</p>
        </div>
        {/* Mela Container with Companies */}
        <MelaContainer melaInfo={melaInfo} companies={companies} />
      </div>
    </div>
  )
}
