import { But<PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { EyeIcon, EyeOffIcon } from "lucide-react"
import { useState } from "react"
import { Checkbox } from "../components/ui/checkbox"
import { useDispatch } from "react-redux"
import { useNavigate } from "react-router-dom"
import { useForm } from "react-hook-form"
import type { AppDispatch } from "@/store/appStore"
import { loginUser } from "@/store/feature/user/userSlice"
import { toast } from "sonner"

type LoginFormValues = {
  username: string
  password: string
}

export function Signin() {
  const [show, setShow] = useState(false)
  const dispatch = useDispatch<AppDispatch>()
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginFormValues>()

  const onSubmit = async (data: LoginFormValues) => {
    try {
      const res = await dispatch(loginUser(data)).unwrap()
      console.log("Login success:", res)
      navigate("/dashboard")
    } catch (error) {
      toast.error("Login failed :" + error)
    }
  }

  return (
    <div
      className="h-screen overflow-hidden w-screen bg-no-repeat bg-cover bg-right flex items-center justify-center relative px-4"
      style={{
        backgroundImage: `url('https://images.unsplash.com/photo-1541871988705-64dfdaf749ac?q=80&w=704&auto=format&fit=crop&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D')`
      }}
    >
      <div className="absolute h-screen w-screen bg-black/50" />

      <div className="p-4 md:p-8 max-w-lg w-full bg-black/40 backdrop-blur-md rounded-xl flex flex-col items-center justify-center">
        <div className="flex items-center gap-4 w-full p-6 pt-0">
          <img
            src="https://staging.skillmissionassam.org/theme/img/asdm.png"
            alt="logo"
            className="size-10 rounded-full"
          />
          <div>
            <h1 className="text-lg font-bold text-white">Assam Skill Development Mission</h1>
            <p className="text-xs text-gray-400">Govt. of Assam</p>
          </div>
        </div>
        <Card className="w-full border-0 shadow-none bg-transparent ">
          <CardHeader>
            <CardTitle className="text-gray-300">Login to your account</CardTitle>
            <CardDescription className="text-sm text-gray-400">
              Enter your email below to login to your account
            </CardDescription>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col gap-6">
                <div className="grid gap-2 text-white">
                  <Label htmlFor="username" className="text-gray-300">
                    Username
                  </Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder="username"
                    className="border-gray-600"
                    {...register("username", { required: "Username is required" })}
                  />
                  {errors.username && (
                    <p className="text-red-400 text-xs mt-1">{errors.username.message}</p>
                  )}
                </div>

                <div className="grid gap-2 text-white">
                  <div className="flex items-center">
                    <Label htmlFor="password" className="text-gray-300">
                      Password
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      id="password"
                      type={show ? "text" : "password"}
                      placeholder="*******"
                      className="border-gray-600 pr-10"
                      {...register("password", { required: "Password is required" })}
                    />
                    <span
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      onClick={() => setShow(!show)}
                    >
                      {show ? <EyeIcon className="size-4" /> : <EyeOffIcon className="size-4" />}
                    </span>
                  </div>
                  {errors.password && (
                    <p className="text-red-400 text-xs mt-1">{errors.password.message}</p>
                  )}
                </div>
              </div>

              <CardFooter className="flex-col gap-2 px-0 pt-6">
                <div className="flex items-center justify-between w-full gap-4">
                  <div className="flex items-center gap-2">
                    <Checkbox id="terms" />
                    <Label htmlFor="terms" className="text-xs md:text-sm text-gray-300">
                      Remember me
                    </Label>
                  </div>
                  <a
                    href="#"
                    className="ml-auto inline-block text-xs md:text-sm underline-offset-4 hover:underline text-gray-300"
                  >
                    Forgot your password?
                  </a>
                </div>

                <Button
                  type="submit"
                  className="w-full bg-emerald-600 hover:bg-emerald-700"
                >
                  Login
                </Button>
              </CardFooter>
            </form>
          </CardContent>
        </Card>

        <div className="text-center text-xs text-gray-500 py-2 pt-6">
          <p>Copyright © 2025 Skill Mission Assam. All rights reserved.</p>
        </div>
      </div>
    </div>
  )
}
