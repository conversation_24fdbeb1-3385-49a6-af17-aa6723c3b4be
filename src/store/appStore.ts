import { configureStore } from "@reduxjs/toolkit";
import sidebarReducer from "@/store/feature/sidebar/sideBarSlice";
import userReducer from "@/store/feature/user/userSlice";
import { jobmelaSlice } from "./feature/jobMela/jobMelaSlice";
export const store = configureStore({
    reducer: {
        sidebar: sidebarReducer,
        user : userReducer,
        jobmela:jobmelaSlice.reducer,
    }
})
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;