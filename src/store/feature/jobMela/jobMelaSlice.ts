import type { RootState } from '@/store/appStore';
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  jobMelaDetails: {},
};

export const jobmelaSlice = createSlice({
  name: 'jobmela',
  initialState,
  reducers: {
    setJobMelaDetails(state, action ) {
      state.jobMelaDetails = action.payload;
    },
  },
});
export const selectJobMelaDetails = (state: RootState) => state.jobmela.jobMelaDetails;
export const { setJobMelaDetails } = jobmelaSlice.actions;
export default jobmelaSlice.reducer;