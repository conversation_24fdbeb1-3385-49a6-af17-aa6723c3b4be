import { createSlice, type PayloadAction } from '@reduxjs/toolkit';

interface SidebarState {
    isOpen: boolean;
    activeItem: string;
}

const initialState: SidebarState = {
    isOpen: true,
    activeItem: 'Dashboard',
};

export const sidebarSlice = createSlice({
    name: 'sidebar',
    initialState,
    reducers: {
        toggleSidebar(state) {
            state.isOpen = !state.isOpen;
        },
        openSidebar(state) {
            state.isOpen = true;
        },
        closeSidebar(state) {
            state.isOpen = false;
        },
        setActiveItem(state, action: PayloadAction<string>) {
            state.activeItem = action.payload;
        },
    },
});

export const { toggleSidebar, openSidebar, closeSidebar, setActiveItem } = sidebarSlice.actions;
export default sidebarSlice.reducer;
