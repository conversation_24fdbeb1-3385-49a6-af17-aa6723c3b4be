
import { createSlice, createAsyncThunk, type PayloadAction } from '@reduxjs/toolkit';
import type { DecodedToken, UserState } from '@/lib/types';
import { loginUserAPI } from './ userAPI';
import Cookies from 'js-cookie';
import { jwtDecode } from "jwt-decode";


const token = Cookies.get('Auth_token') || null;
const decodedToken = token ? jwtDecode<DecodedToken>(token) : null;
const initialState: UserState = {
  user: null,
  username: decodedToken?.username || null,
  isEnabled: decodedToken?.isEnabled || null,
  lastLogin: decodedToken?.lastLogin || null,
  token: token || null,
  status: 'idle',
  role: decodedToken?.role || null,
  error: null,
};

export const loginUser = createAsyncThunk(
  'auth/login',
  async ({ username, password }: { username: string; password: string }, thunkAPI) => {
    try {
      const response = await loginU<PERSON><PERSON><PERSON>(username, password);
      return response.data.data;
    } catch (err: any) {
      return thunkAPI.rejectWithValue(err.response?.data?.message || 'Login failed ');
    }
  }
);

const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    logout(state) {
      state.token = null
      state.username = null
      state.role = null
      state.isEnabled = null
      state.lastLogin = null
      state.status = 'idle'
      state.error = null
      Cookies.remove("Auth_token")
    },
  },
  extraReducers: builder => {
    builder
      .addCase(loginUser.pending, state => {
        state.status = 'loading';
        state.error = null;
      })
      .addCase(
        loginUser.fulfilled,
        (
          state,
          action: PayloadAction<{
            token: string;
            expiresIn: string;
            user: {
              userId: number;
              username: string;
              role: string;
              email: string | null;
              isEnabled: boolean;
              lastLogin: string | null;
            };
          }>
        ) => {
          state.status = 'succeeded';
          state.token = action.payload.token;
          state.user = action.payload.user;
          state.username = action.payload.user.username;
          state.isEnabled = action.payload.user.isEnabled;
          state.lastLogin = action.payload.user.lastLogin;
          state.role = action.payload.user.role;
          Cookies.set('Auth_token', action.payload.token);
        }

      )
      .addCase(loginUser.rejected, (state, action) => {
        state.status = 'failed';
        state.error = action.payload as string;
      });
  },
});

export const { logout } = userSlice.actions;
export default userSlice.reducer;
