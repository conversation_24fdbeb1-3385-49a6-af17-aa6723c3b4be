
import axios from 'axios';
import Cookies from 'js-cookie';

export const postData = async ({
  url,
  data,
  headers = {},
}: {
  url: string;
  data: any;
  headers?: Record<string, string>;
}) => {
  try {
    const response = await axios.post(`${import.meta.env.VITE_API_URL}${url}`, data, {
      headers: {
        Authorization: `Bearer ${Cookies.get('Auth_token') || ''}`,
        ...headers,
      },
    });
    return response.data;
  } catch (error: any) {
    throw new Error(error?.response?.data?.message || 'Failed to post data');
  }
};
  